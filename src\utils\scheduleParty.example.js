/**
 * Example usage of the improved scheduleParty utility
 * This file demonstrates how to use the new return values and error handling
 */

const { scheduleParty, deleteScheduledParty, listScheduledParties, getScheduledParty } = require('./scheduleParty.util');

// Example party object
const exampleParty = {
    _id: '507f1f77bcf86cd799439011',
    time: new Date(Date.now() + 4 * 60 * 60 * 1000).getTime(), // 4 hours from now
    name: 'Birthday Party',
    location: 'Central Park'
};

async function demonstrateUsage() {
    console.log('=== Schedule Party Utility Demo ===\n');

    // 1. Schedule a party
    console.log('1. Scheduling a party...');
    const scheduleResult = await scheduleParty(exampleParty);
    
    if (scheduleResult.success) {
        console.log('✅ Party scheduled successfully!');
        console.log(`   Scheduled time: ${scheduleResult.scheduledTime}`);
        console.log(`   Rule name: ${scheduleResult.ruleName}`);
        console.log(`   Cron expression: ${scheduleResult.cronExpression}\n`);
    } else {
        console.log('❌ Failed to schedule party:');
        console.log(`   Error: ${scheduleResult.error}`);
        console.log(`   Error code: ${scheduleResult.errorCode}\n`);
        return; // Exit if scheduling failed
    }

    // 2. Get details of the scheduled party
    console.log('2. Getting party details...');
    const detailsResult = await getScheduledParty(exampleParty._id);
    
    if (detailsResult.success && detailsResult.exists) {
        console.log('✅ Party details retrieved:');
        console.log(`   Rule: ${detailsResult.rule.name}`);
        console.log(`   Schedule: ${detailsResult.rule.scheduleExpression}`);
        console.log(`   State: ${detailsResult.rule.state}`);
        console.log(`   Targets: ${detailsResult.targets.length}\n`);
    } else if (detailsResult.success && !detailsResult.exists) {
        console.log('ℹ️  Party is not scheduled\n');
    } else {
        console.log('❌ Failed to get party details:');
        console.log(`   Error: ${detailsResult.error}\n`);
    }

    // 3. List all scheduled parties
    console.log('3. Listing all scheduled parties...');
    const listResult = await listScheduledParties();
    
    if (listResult.success) {
        console.log(`✅ Found ${listResult.count} scheduled parties:`);
        listResult.parties.forEach(party => {
            console.log(`   - Party ${party.partyId}: ${party.scheduleExpression} (${party.state})`);
        });
        console.log('');
    } else {
        console.log('❌ Failed to list parties:');
        console.log(`   Error: ${listResult.error}\n`);
    }

    // 4. Delete the scheduled party
    console.log('4. Deleting the scheduled party...');
    const deleteResult = await deleteScheduledParty(exampleParty);
    
    if (deleteResult.success) {
        console.log('✅ Party deleted successfully!');
        console.log(`   Message: ${deleteResult.message}\n`);
    } else {
        console.log('❌ Failed to delete party:');
        console.log(`   Error: ${deleteResult.error}`);
        console.log(`   Error code: ${deleteResult.errorCode}\n`);
    }

    // 5. Verify deletion
    console.log('5. Verifying deletion...');
    const verifyResult = await getScheduledParty(exampleParty._id);
    
    if (verifyResult.success && !verifyResult.exists) {
        console.log('✅ Confirmed: Party is no longer scheduled\n');
    } else if (verifyResult.success && verifyResult.exists) {
        console.log('⚠️  Warning: Party still appears to be scheduled\n');
    } else {
        console.log('❌ Failed to verify deletion:');
        console.log(`   Error: ${verifyResult.error}\n`);
    }
}

// Example error handling in application code
async function handlePartyCreation(partyData) {
    console.log('=== Application Integration Example ===\n');
    
    // Simulate creating party in database first
    console.log('Creating party in database...');
    // const savedParty = await PartyModel.create(partyData);
    
    // Then schedule the party
    const scheduleResult = await scheduleParty(partyData);
    
    if (!scheduleResult.success) {
        console.log('⚠️  Party created but scheduling failed!');
        console.log('This requires manual intervention or retry logic.');
        console.log(`Error: ${scheduleResult.error}`);
        
        // You could:
        // 1. Mark party as "scheduling_failed" in database
        // 2. Add to a retry queue
        // 3. Send alert to administrators
        // 4. Return error to user with specific message
        
        return {
            partyCreated: true,
            schedulingFailed: true,
            error: scheduleResult.error
        };
    }
    
    console.log('✅ Party created and scheduled successfully!');
    return {
        partyCreated: true,
        schedulingFailed: false,
        scheduleDetails: scheduleResult
    };
}

// Example validation error handling
async function demonstrateValidation() {
    console.log('=== Validation Examples ===\n');
    
    // Test with invalid party object
    console.log('Testing with invalid party (missing _id)...');
    const invalidResult1 = await scheduleParty({ time: Date.now() });
    console.log(`Result: ${invalidResult1.success ? 'Success' : 'Failed'}`);
    console.log(`Error: ${invalidResult1.error}\n`);
    
    // Test with past date
    console.log('Testing with past date...');
    const pastParty = {
        _id: 'test123',
        time: Date.now() - 60 * 60 * 1000 // 1 hour ago
    };
    const invalidResult2 = await scheduleParty(pastParty);
    console.log(`Result: ${invalidResult2.success ? 'Success' : 'Failed'}`);
    console.log(`Error: ${invalidResult2.error}\n`);
    
    // Test with invalid date
    console.log('Testing with invalid date...');
    const invalidDateParty = {
        _id: 'test456',
        time: 'not-a-date'
    };
    const invalidResult3 = await scheduleParty(invalidDateParty);
    console.log(`Result: ${invalidResult3.success ? 'Success' : 'Failed'}`);
    console.log(`Error: ${invalidResult3.error}\n`);
}

// Run examples if this file is executed directly
if (require.main === module) {
    (async () => {
        try {
            await demonstrateUsage();
            await handlePartyCreation(exampleParty);
            await demonstrateValidation();
        } catch (error) {
            console.error('Demo failed:', error);
        }
    })();
}

module.exports = {
    demonstrateUsage,
    handlePartyCreation,
    demonstrateValidation
};
