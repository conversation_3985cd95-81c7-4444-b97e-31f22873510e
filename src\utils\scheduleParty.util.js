const AWS = require('aws-sdk');

// Validate required environment variables
if (!process.env.AWS_REGION) {
    throw new Error('AWS_REGION environment variable is required');
}

if (!process.env.GO_LIVE_FUNCTION_ARN) {
    throw new Error('GO_LIVE_FUNCTION_ARN environment variable is required');
}

const eventBridge = new AWS.EventBridge({region: process.env.AWS_REGION});

/**
 * Validates party object and calculates go-live time
 * @param {Object} party - Party object with time and _id properties
 * @returns {Object} - Validation result with goLiveTime if valid
 */
function validatePartyAndCalculateTime(party) {
    // Validate party object
    if (!party?.time || !party?._id) {
        throw new Error('Invalid party object: missing time or _id');
    }

    // Validate and parse party time
    const partyTime = new Date(party.time);
    if (isNaN(partyTime.getTime())) {
        throw new Error('Invalid party time format');
    }

    // Calculate go-live time (3 hours before party)
    const goLiveTime = new Date(partyTime.getTime() - 3 * 60 * 60 * 1000);

    // Ensure we're not scheduling in the past (with 1 minute buffer)
    const now = new Date();
    const oneMinuteFromNow = new Date(now.getTime() + 60 * 1000);

    if (goLiveTime < oneMinuteFromNow) {
        throw new Error(`Cannot schedule party in the past. Go-live time would be ${goLiveTime.toISOString()}`);
    }

    return { goLiveTime, partyTime };
}

/**
 * Checks if an EventBridge rule already exists
 * @param {string} ruleName - Name of the rule to check
 * @returns {boolean} - True if rule exists
 */
async function ruleExists(ruleName) {
    try {
        await eventBridge.describeRule({ Name: ruleName }).promise();
        return true;
    } catch (error) {
        if (error.code === 'ResourceNotFoundException') {
            return false;
        }
        throw error; // Re-throw other errors
    }
}

/**
 * Schedules a party to go live 3 hours before the party time
 * @param {Object} party - Party object with time and _id properties
 * @returns {Object} - Result object with success status and details
 */
async function scheduleParty(party) {
    try {
        // Validate input and calculate times
        const { goLiveTime } = validatePartyAndCalculateTime(party);
        const ruleName = `goLive-${party._id}`;

        // Check if rule already exists
        if (await ruleExists(ruleName)) {
            console.log(`Rule ${ruleName} already exists, updating schedule`);
            // Delete existing rule first to avoid conflicts
            const deleteResult = await deleteScheduledParty(party);
            if (!deleteResult.success) {
                return {
                    success: false,
                    error: `Failed to delete existing rule: ${deleteResult.error}`
                };
            }
        }

        // Format the date for cron expression
        const minute = goLiveTime.getUTCMinutes();
        const hour = goLiveTime.getUTCHours();
        const day = goLiveTime.getUTCDate();
        const month = goLiveTime.getUTCMonth() + 1; // getMonth() returns 0-11
        const year = goLiveTime.getUTCFullYear();

        // Create cron expression: "minute hour day month ? year"
        const cronExpression = `${minute} ${hour} ${day} ${month} ? ${year}`;

        // Create the rule
        await eventBridge.putRule({
            Name: ruleName,
            ScheduleExpression: `cron(${cronExpression})`,
            State: 'ENABLED',
            Description: `Go live trigger for party ${party._id}`
        }).promise();

        try {
            // Add target to the rule
            await eventBridge.putTargets({
                Rule: ruleName,
                Targets: [{
                    Id: "1",
                    Arn: process.env.GO_LIVE_FUNCTION_ARN,
                    Input: JSON.stringify({ partyId: party._id.toString() })
                }]
            }).promise();
        } catch (targetError) {
            // If target creation fails, clean up the rule
            console.error(`Failed to add targets for rule ${ruleName}, cleaning up rule`);
            try {
                await eventBridge.deleteRule({ Name: ruleName, Force: true }).promise();
            } catch (cleanupError) {
                console.error(`Failed to cleanup rule ${ruleName}:`, cleanupError);
            }
            throw targetError;
        }

        console.log(`Successfully scheduled party ${party._id} to go live at ${goLiveTime.toISOString()}`);

        return {
            success: true,
            scheduledTime: goLiveTime.toISOString(),
            ruleName: ruleName,
            cronExpression: cronExpression
        };

    } catch (error) {
        console.error(`Error scheduling party ${party._id}:`, error);

        return {
            success: false,
            error: error.message,
            errorCode: error.code || 'UNKNOWN_ERROR'
        };
    }
}

/**
 * Deletes a scheduled party's EventBridge rule and targets
 * @param {Object} party - Party object with _id property
 * @returns {Object} - Result object with success status and details
 */
async function deleteScheduledParty(party) {
    if (!party?._id) {
        return {
            success: false,
            error: 'Invalid party object: missing _id'
        };
    }

    const ruleName = `goLive-${party._id}`;

    try {
        // Check if rule exists first
        const exists = await ruleExists(ruleName);
        if (!exists) {
            console.log(`Rule ${ruleName} does not exist, nothing to delete`);
            return {
                success: true,
                message: 'Rule did not exist'
            };
        }

        // First remove the targets
        try {
            await eventBridge.removeTargets({
                Rule: ruleName,
                Ids: ["1"]
            }).promise();
        } catch (targetError) {
            // If targets don't exist, that's okay, continue with rule deletion
            if (targetError.code !== 'ResourceNotFoundException') {
                console.error(`Error removing targets for rule ${ruleName}:`, targetError);
                // Continue anyway to try to delete the rule
            }
        }

        // Then delete the rule
        await eventBridge.deleteRule({
            Name: ruleName,
            Force: true
        }).promise();

        console.log(`Successfully deleted scheduled party ${party._id}`);

        return {
            success: true,
            ruleName: ruleName,
            message: 'Rule and targets deleted successfully'
        };

    } catch (error) {
        console.error(`Error deleting scheduled party ${party._id}:`, error);

        // Handle specific AWS errors
        if (error.code === 'ResourceNotFoundException') {
            return {
                success: true,
                message: 'Rule was already deleted or did not exist'
            };
        }

        return {
            success: false,
            error: error.message,
            errorCode: error.code || 'UNKNOWN_ERROR'
        };
    }
}

/**
 * Lists all scheduled party rules
 * @returns {Object} - Result object with success status and list of rules
 */
async function listScheduledParties() {
    try {
        const response = await eventBridge.listRules({
            NamePrefix: 'goLive-'
        }).promise();

        const scheduledParties = response.Rules.map(rule => ({
            ruleName: rule.Name,
            partyId: rule.Name.replace('goLive-', ''),
            scheduleExpression: rule.ScheduleExpression,
            state: rule.State,
            description: rule.Description
        }));

        return {
            success: true,
            count: scheduledParties.length,
            parties: scheduledParties
        };

    } catch (error) {
        console.error('Error listing scheduled parties:', error);
        return {
            success: false,
            error: error.message,
            errorCode: error.code || 'UNKNOWN_ERROR'
        };
    }
}

/**
 * Gets details of a specific scheduled party
 * @param {string} partyId - The party ID to check
 * @returns {Object} - Result object with success status and rule details
 */
async function getScheduledParty(partyId) {
    if (!partyId) {
        return {
            success: false,
            error: 'Party ID is required'
        };
    }

    const ruleName = `goLive-${partyId}`;

    try {
        const exists = await ruleExists(ruleName);
        if (!exists) {
            return {
                success: true,
                exists: false,
                message: 'Party is not scheduled'
            };
        }

        const ruleResponse = await eventBridge.describeRule({ Name: ruleName }).promise();
        const targetsResponse = await eventBridge.listTargetsByRule({ Rule: ruleName }).promise();

        return {
            success: true,
            exists: true,
            rule: {
                name: ruleResponse.Name,
                scheduleExpression: ruleResponse.ScheduleExpression,
                state: ruleResponse.State,
                description: ruleResponse.Description
            },
            targets: targetsResponse.Targets
        };

    } catch (error) {
        console.error(`Error getting scheduled party ${partyId}:`, error);
        return {
            success: false,
            error: error.message,
            errorCode: error.code || 'UNKNOWN_ERROR'
        };
    }
}

module.exports = {
    scheduleParty,
    deleteScheduledParty,
    listScheduledParties,
    getScheduledParty,
    // Export utility functions for testing
    validatePartyAndCalculateTime,
    ruleExists
};